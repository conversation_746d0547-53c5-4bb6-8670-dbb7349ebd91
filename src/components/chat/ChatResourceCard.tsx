'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Entity } from '@/types/entity';
import { Star, ArrowRight, ExternalLink, Sparkles, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import UpvoteButton from '@/components/common/UpvoteButton';

interface ChatResourceCardProps {
  entity: Entity;
  index?: number;
}

const ChatResourceCard: React.FC<ChatResourceCardProps> = ({ entity, index = 0 }) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const fallbackImage = '/images/placeholder-logo.png';
  const imageSrc = imageError ? fallbackImage : (entity?.logoUrl || fallbackImage);

  const entityName = entity?.name || 'Unnamed Entity';
  const entityDescription = entity?.shortDescription || entity?.description || 'No description available.';
  const entityTypeName = entity?.entityType?.name || 'N/A';
  const averageRating = typeof entity?.avgRating === 'number' ? entity.avgRating.toFixed(1) : 'N/A';
  const reviewCount = typeof entity?.reviewCount === 'number' ? entity.reviewCount : 0;

  const handleImageError = () => setImageError(true);
  const handleImageLoad = () => setImageLoaded(true);

  return (
    <Card 
      className="group overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-white/95 backdrop-blur-sm"
      style={{ 
        animationDelay: `${index * 100}ms`,
        animation: 'fadeInUp 0.5s ease-out forwards'
      }}
    >
      <CardContent className="p-0">
        <div className="flex gap-4 p-4">
          {/* Logo Section */}
          <div className="flex-shrink-0">
            <div className="relative w-16 h-16 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl overflow-hidden">
              {!imageLoaded && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-4 h-4 border-2 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
                </div>
              )}
              <Image
                src={imageSrc}
                alt={`${entityName} logo`}
                fill
                sizes="64px"
                style={{ objectFit: "contain" }}
                className={`p-2 transition-all duration-200 group-hover:scale-110 ${
                  imageLoaded ? 'opacity-100' : 'opacity-0'
                }`}
                onError={handleImageError}
                onLoad={handleImageLoad}
              />
            </div>
          </div>

          {/* Content Section */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2 mb-2">
              <h4 className="font-semibold text-gray-900 text-sm leading-tight line-clamp-1" title={entityName}>
                {entityName}
              </h4>
              
              {/* Type Badge */}
              <span className="flex-shrink-0 inline-flex items-center px-2 py-1 text-xs font-medium bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 rounded-full">
                {entityTypeName}
              </span>
            </div>

            {/* Description */}
            <p className="text-gray-600 text-xs leading-relaxed line-clamp-2 mb-3" title={entityDescription}>
              {entityDescription}
            </p>

            {/* Features and Badges */}
            <div className="flex flex-wrap gap-1 mb-3">
              {entity?.hasFreeTier && (
                <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-700 rounded-md">
                  Free Tier
                </span>
              )}
              {entity?.features?.slice(0, 2).map((feature) => (
                <span
                  key={feature?.id}
                  className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-700 rounded-md"
                >
                  {feature?.name}
                </span>
              ))}
              {(entity?.features?.length || 0) > 2 && (
                <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-md">
                  +{(entity?.features?.length || 0) - 2} more
                </span>
              )}
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {/* Rating */}
                <div className="flex items-center gap-1">
                  <Star className="w-3 h-3 text-amber-400 fill-current" />
                  <span className="text-xs font-medium text-gray-700">{averageRating}</span>
                  <span className="text-xs text-gray-500">({reviewCount})</span>
                </div>

                {/* Upvote */}
                <UpvoteButton
                  entityId={entity.id}
                  initialUpvoteCount={entity.upvoteCount || 0}
                  size="sm"
                  variant="compact"
                />
              </div>

              {/* External Link Indicator */}
              {entity?.websiteUrl && (
                <ExternalLink className="w-3 h-3 text-gray-400" />
              )}
            </div>
          </div>
        </div>

        {/* Action Section */}
        <div className="border-t border-gray-100 bg-gradient-to-r from-gray-50 to-gray-50/50 p-3">
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              <Sparkles className="w-4 h-4 text-indigo-500" />
              <span className="text-xs text-gray-600 font-medium">AI Recommended</span>
            </div>
            
            <Link
              href={`/entities/${entity.slug || entity.id}`}
              className="inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 group"
            >
              View Details
              <ArrowRight className="w-3 h-3 transition-transform duration-200 group-hover:translate-x-0.5" />
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Add the animation keyframes to global CSS if not already present
const styles = `
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

export default ChatResourceCard;
