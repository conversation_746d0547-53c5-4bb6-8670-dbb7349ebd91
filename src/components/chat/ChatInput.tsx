'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Send, Loader2 } from 'lucide-react';

interface ChatInputProps {
  userInput: string;
  setUserInput: (value: string) => void;
  onSubmit: () => void;
  isLoading: boolean;
  placeholder?: string;
  disabled?: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({
  userInput,
  setUserInput,
  onSubmit,
  isLoading,
  placeholder = "Ask me about AI tools, courses, or anything else...",
  disabled = false,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  // Auto-resize textarea based on content
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [userInput]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!userInput.trim() || isLoading || disabled) return;
    onSubmit();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const canSubmit = userInput.trim().length > 0 && !isLoading && !disabled;

  return (
    <div className="p-4 sm:p-6">
      <form onSubmit={handleSubmit} className="flex flex-col gap-3 max-w-4xl mx-auto">
        {/* Input Area */}
        <div className={`relative flex items-end gap-3 p-3 sm:p-4 border-2 rounded-2xl transition-all duration-200 shadow-lg backdrop-blur-sm ${
          isFocused
            ? 'border-indigo-300 bg-white/95 shadow-xl'
            : 'border-gray-200/50 bg-white/90'
        }`}>
          {/* Textarea */}
          <div className="flex-1">
            <Textarea
              ref={textareaRef}
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder={placeholder}
              disabled={disabled}
              className="min-h-[40px] max-h-[120px] resize-none border-0 bg-transparent p-0 text-sm placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
              style={{ 
                height: 'auto',
                overflow: 'hidden'
              }}
            />
          </div>

          {/* Send Button */}
          <Button
            type="submit"
            size="sm"
            disabled={!canSubmit}
            className={`flex-shrink-0 h-10 w-10 p-0 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 ${
              canSubmit
                ? 'bg-gradient-to-br from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            }`}
          >
            {isLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <Send className="h-5 w-5" />
            )}
          </Button>
        </div>

        {/* Helper Text */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>
            Press Enter to send, Shift+Enter for new line
          </span>
          <span className={`${
            userInput.length > 500 ? 'text-amber-600' : 
            userInput.length > 800 ? 'text-red-600' : 'text-gray-400'
          }`}>
            {userInput.length}/1000
          </span>
        </div>
      </form>
    </div>
  );
};

export default ChatInput;
